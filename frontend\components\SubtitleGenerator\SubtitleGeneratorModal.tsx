/**
 * 字幕生成主模态窗口组件
 * 包含文件上传、参数配置、任务处理和结果显示的完整流程
 */

"use client"

import React, { useState, useCallback, useRef, useEffect } from 'react'
import { FileText, Upload, Settings, Download, AlertCircle, CheckCircle } from 'lucide-react'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Card, CardContent } from '@/components/ui/card'
import { useToast } from '@/hooks/use-toast'

import { SubtitleService } from '@/lib/subtitle-service'
import FileUploadArea from './FileUploadArea'
import ParameterConfigForm from './ParameterConfigForm'
import { DEFAULT_CONFIG, CONSTANTS } from './types'
import type {
  SubtitleGeneratorState,
  ComponentStep,
  SubtitleConfig,
  StatusResponse
} from './types'

interface SubtitleGeneratorModalProps {
  isOpen: boolean
  onClose: () => void
  onTaskCreated?: (taskId: string) => void
  onTaskCompleted?: (taskId: string, status: 'complete' | 'failed', srtFiles?: any[]) => void
}

const SubtitleGeneratorModal: React.FC<SubtitleGeneratorModalProps> = ({
  isOpen,
  onClose,
  onTaskCreated,
  onTaskCompleted
}) => {
  const { toast } = useToast()
  const pollingTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  // 初始化字幕服务配置
  React.useEffect(() => {
    if (isOpen) {
      try {
        SubtitleService.init()
      } catch (error) {
        console.error('字幕服务初始化失败:', error)
        toast({
          title: "配置错误",
          description: error instanceof Error ? error.message : '字幕服务配置错误',
          variant: "destructive"
        })
      }
    }
  }, [isOpen, toast])
  
  // 主要状态管理
  const [state, setState] = useState<SubtitleGeneratorState>({
    isOpen: false,
    currentStep: 'upload',
    selectedFile: null,
    fileValidation: { isValid: true },
    config: { ...DEFAULT_CONFIG },
    taskId: null,
    progress: 0,
    status: 'pending',
    isPolling: false
  })

  // 重置状态
  const resetState = useCallback(() => {
    // 清除轮询
    if (pollingTimeoutRef.current) {
      clearTimeout(pollingTimeoutRef.current)
    }
    
    setState({
      isOpen: false,
      currentStep: 'upload',
      selectedFile: null,
      fileValidation: { isValid: true },
      config: { ...DEFAULT_CONFIG },
      taskId: null,
      progress: 0,
      status: 'pending',
      isPolling: false
    })
  }, [])

  // 处理模态窗口关闭
  const handleClose = useCallback(() => {
    // 如果正在处理任务，询问用户是否确认关闭
    if (state.currentStep === 'processing' && state.isPolling) {
      const confirmed = window.confirm('任务正在处理中，关闭窗口不会停止任务。您可以在任务中心查看进度。确认关闭吗？')
      if (!confirmed) return
    }
    
    resetState()
    onClose()
  }, [state.currentStep, state.isPolling, resetState, onClose])

  // 文件选择处理
  const handleFileSelect = useCallback((file: File) => {
    const validation = SubtitleService.validateFile(file)
    
    setState(prev => ({
      ...prev,
      selectedFile: file,
      fileValidation: validation,
      currentStep: validation.isValid ? 'config' : 'upload'
    }))

    // 显示验证结果
    if (!validation.isValid) {
      toast({
        title: "文件验证失败",
        description: validation.error,
        variant: "destructive"
      })
    } else if (validation.warnings && validation.warnings.length > 0) {
      toast({
        title: "注意",
        description: validation.warnings.join(', '),
        variant: "default"
      })
    }
  }, [toast])

  // 配置更新处理
  const handleConfigChange = useCallback((newConfig: Partial<SubtitleConfig>) => {
    setState(prev => ({
      ...prev,
      config: { ...prev.config, ...newConfig }
    }))
  }, [])

  // 重新选择文件
  const handleReselectFile = useCallback(() => {
    setState(prev => ({
      ...prev,
      currentStep: 'upload',
      selectedFile: null,
      fileValidation: { isValid: true }
      // 保留配置参数不变
    }))
  }, [])

  // 开始生成字幕
  const handleStartGeneration = useCallback(async () => {
    if (!state.selectedFile) {
      toast({
        title: "错误",
        description: "请先选择文件",
        variant: "destructive"
      })
      return
    }

    try {
      setState(prev => ({ ...prev, currentStep: 'processing', progress: 0 }))

      // 上传文件并创建任务
      const response = await SubtitleService.uploadAudio(state.selectedFile, state.config)
      
      setState(prev => ({
        ...prev,
        taskId: response.task_id,
        status: response.status,
        isPolling: true
      }))

      // 通知父组件任务已创建
      if (onTaskCreated) {
        onTaskCreated(response.task_id)
      }

      // 开始轮询任务状态
      startPolling(response.task_id)

      toast({
        title: "任务已创建",
        description: "开始处理您的文件，请稍候...",
        variant: "default"
      })

    } catch (error) {
      console.error('开始生成字幕失败:', error)
      setState(prev => ({
        ...prev,
        currentStep: 'error',
        error: error instanceof Error ? error.message : '未知错误',
        isPolling: false
      }))

      toast({
        title: "生成失败",
        description: error instanceof Error ? error.message : '未知错误',
        variant: "destructive"
      })
    }
  }, [state.selectedFile, state.config, onTaskCreated, toast])

  // 开始轮询任务状态
  const startPolling = useCallback((taskId: string) => {
    const poll = async () => {
      try {
        const status = await SubtitleService.getTaskStatus(taskId)

        // 处理任务状态响应

        setState(prev => ({
          ...prev,
          progress: status.progress || 0,
          status: status.status,
          result: status
        }))

        if (status.status === 'completed') {
          setState(prev => ({
            ...prev,
            currentStep: 'completed',
            isPolling: false
          }))

          // 通知主页面任务完成，同步到任务中心
          if (onTaskCompleted && taskId) {
            onTaskCompleted(taskId, 'complete', status.srt_files)
          }

          toast({
            title: "字幕生成完成",
            description: "您可以下载生成的字幕文件了",
            variant: "default"
          })
          
        } else if (status.status === 'failed') {
          setState(prev => ({
            ...prev,
            currentStep: 'error',
            error: status.error || '任务处理失败',
            isPolling: false
          }))

          // 通知主页面任务失败，同步到任务中心
          if (onTaskCompleted && taskId) {
            onTaskCompleted(taskId, 'failed')
          }

          toast({
            title: "生成失败",
            description: status.error || '任务处理失败',
            variant: "destructive"
          })
          
        } else if (status.status === 'processing' || status.status === 'pending') {
          // 继续轮询
          pollingTimeoutRef.current = setTimeout(poll, CONSTANTS.POLLING_INTERVAL)
        }
        
      } catch (error) {
        console.error('[SUBTITLE-MODAL] 轮询任务状态失败:', error)
        setState(prev => ({
          ...prev,
          currentStep: 'error',
          error: error instanceof Error ? error.message : '查询任务状态失败',
          isPolling: false
        }))

        // 通知主页面任务失败
        if (onTaskCompleted && state.taskId) {
          onTaskCompleted(state.taskId, 'failed')
        }
      }
    }

    // 开始轮询
    poll()
  }, [toast, onTaskCompleted, state.taskId])

  // 下载SRT文件
  const handleDownload = useCallback(async (filename: string, taskId: string) => {
    try {
      const blob = await SubtitleService.downloadSRT(taskId, filename)
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = filename
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)

      toast({
        title: "下载成功",
        description: `文件 ${filename} 已下载`,
        variant: "default"
      })
    } catch (error) {
      console.error('下载文件失败:', error)
      toast({
        title: "下载失败",
        description: error instanceof Error ? error.message : '下载失败',
        variant: "destructive"
      })
    }
  }, [toast])

  // 清理副作用
  useEffect(() => {
    return () => {
      if (pollingTimeoutRef.current) {
        clearTimeout(pollingTimeoutRef.current)
      }
    }
  }, [])

  // 渲染步骤指示器
  const renderStepIndicator = () => {
    const steps = [
      { key: 'upload', label: '上传文件', icon: Upload },
      { key: 'config', label: '参数配置', icon: Settings },
      { key: 'processing', label: '处理中', icon: FileText },
      { key: 'completed', label: '完成', icon: CheckCircle }
    ]

    return (
      <div className="flex items-center justify-center mb-6">
        {steps.map((step, index) => {
          const Icon = step.icon
          const isActive = state.currentStep === step.key
          const isCompleted = steps.findIndex(s => s.key === state.currentStep) > index
          const isError = state.currentStep === 'error'

          return (
            <React.Fragment key={step.key}>
              <div className={`
                flex items-center justify-center w-8 h-8 rounded-full border-2 transition-all duration-300
                ${isActive ? 'border-purple-500 bg-purple-500 text-white' : 
                  isCompleted ? 'border-green-500 bg-green-500 text-white' :
                  isError && isActive ? 'border-red-500 bg-red-500 text-white' :
                  'border-gray-300 bg-gray-100 text-gray-400'}
              `}>
                <Icon className="w-4 h-4" />
              </div>
              
              {index < steps.length - 1 && (
                <div className={`
                  w-12 h-0.5 mx-2 transition-all duration-300
                  ${isCompleted ? 'bg-green-500' : 'bg-gray-300'}
                `} />
              )}
            </React.Fragment>
          )
        })}
      </div>
    )
  }

  return (
    <Dialog open={isOpen} onOpenChange={(open) => {
      if (!open) {
        handleClose()
      }
    }}>
      <DialogContent
        className="max-w-4xl max-h-[90vh] overflow-y-auto"
        onPointerDownOutside={(e) => e.preventDefault()}
        onEscapeKeyDown={(e) => e.preventDefault()}
      >
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-xl font-bold">
            <FileText className="w-6 h-6 text-purple-500" />
            字幕生成
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* 步骤指示器 */}
          {renderStepIndicator()}

          {/* 错误显示 */}
          {state.currentStep === 'error' && state.error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{state.error}</AlertDescription>
            </Alert>
          )}

          {/* 主要内容区域 */}
          <div className="min-h-[400px]">
            {/* 文件上传步骤 */}
            {state.currentStep === 'upload' && (
              <FileUploadArea
                onFileSelect={handleFileSelect}
                selectedFile={state.selectedFile}
                validation={state.fileValidation}
                disabled={state.isPolling}
              />
            )}

            {/* 参数配置步骤 */}
            {state.currentStep === 'config' && (
              <div className="space-y-4">
                <div className="text-center mb-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    配置字幕生成参数
                  </h3>
                </div>

                {/* 文件信息展示区域 */}
                {state.selectedFile && (
                  <Card className="bg-blue-50 border-blue-200 mb-6">
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <FileText className="w-5 h-5 text-blue-600" />
                          <div>
                            <p className="font-medium text-gray-900">{state.selectedFile.name}</p>
                            <p className="text-sm text-gray-500">
                              {SubtitleService.formatFileSize(state.selectedFile.size)} • {state.selectedFile.name.split('.').pop()?.toUpperCase()}
                            </p>
                          </div>
                        </div>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={handleReselectFile}
                          className="text-blue-600 border-blue-300 hover:bg-blue-50"
                        >
                          <Upload className="w-4 h-4 mr-1" />
                          重新选择
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                )}
                <ParameterConfigForm
                  config={state.config}
                  onChange={handleConfigChange}
                  disabled={state.isPolling}
                />
              </div>
            )}

            {/* 处理中步骤 */}
            {state.currentStep === 'processing' && (
              <div className="flex flex-col items-center justify-center space-y-6">
                <div className="text-center">
                  <FileText className="w-16 h-16 text-purple-500 mx-auto mb-4 animate-pulse" />
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    正在处理您的文件
                  </h3>
                  <p className="text-sm text-gray-600 mb-4">
                    文件: {state.selectedFile?.name}
                  </p>
                </div>

                <div className="w-full max-w-md space-y-4">
                  <Progress value={state.progress} className="w-full" />
                  <div className="flex justify-between text-sm text-gray-600">
                    <span>进度: {state.progress}%</span>
                    <span>任务ID: {state.taskId?.slice(0, 8)}...</span>
                  </div>
                </div>

                <div className="text-center text-sm text-gray-500">
                  <p>处理时间取决于文件大小，请耐心等待...</p>
                  <p>您可以关闭此窗口，在任务中心查看进度</p>
                </div>
              </div>
            )}

            {/* 完成步骤 */}
            {state.currentStep === 'completed' && (
              <div className="flex flex-col items-center justify-center space-y-6">
                <div className="text-center">
                  <CheckCircle className="w-16 h-16 text-green-500 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    字幕生成完成！
                  </h3>
                  <p className="text-sm text-gray-600">
                    处理时间: {state.result?.processing_time ?
                      SubtitleService.formatProcessingTime(state.result.processing_time) :
                      '未知'
                    }
                  </p>
                </div>

                {/* 下载区域 */}
                {state.result?.srt_files && state.result.srt_files.length > 0 ? (
                  <>
                    <div className="w-full max-w-md space-y-3">
                      {state.result.srt_files.map((file, index) => (
                        <div key={index} className="flex items-center justify-between p-4 bg-gradient-to-r from-green-50 to-blue-50 rounded-lg border-2 border-green-200 shadow-sm">
                          <div className="text-left">
                            <p className="font-medium text-gray-900">{file.filename}</p>
                            <p className="text-sm text-gray-500">
                              大小: {SubtitleService.formatFileSize(file.file_size)}
                              {file.duration && ` • 时长: ${Math.round(file.duration)}秒`}
                            </p>
                          </div>
                          <Button
                            onClick={() => handleDownload(file.filename, state.taskId!)}
                            size="lg"
                            className="bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600 text-white font-semibold px-6 py-2 shadow-lg transform hover:scale-105 transition-all duration-200"
                          >
                            <Download className="w-5 h-5 mr-2" />
                            立即下载
                          </Button>
                        </div>
                      ))}
                    </div>

                    {/* 批量下载按钮 */}
                    {state.result.srt_files.length > 1 && (
                      <Button
                        onClick={() => {
                          state.result!.srt_files!.forEach(file => {
                            handleDownload(file.filename, state.taskId!)
                          })
                        }}
                        variant="outline"
                        className="w-full mt-4 border-2 border-blue-300 text-blue-600 hover:bg-blue-50"
                      >
                        <Download className="w-4 h-4 mr-2" />
                        下载所有文件
                      </Button>
                    )}

                    <div className="text-center text-sm text-gray-500">
                      <p>文件已保存到任务中心，您可以随时重新下载</p>
                    </div>
                  </>
                ) : (
                  <div className="w-full max-w-md text-center">
                    <div className="p-4 bg-yellow-50 rounded-lg border border-yellow-200">
                      <p className="text-sm text-yellow-800 mb-2">
                        字幕文件正在准备中...
                      </p>
                      <p className="text-xs text-yellow-600">
                        请稍候或前往任务中心查看
                      </p>
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* 错误步骤 */}
            {state.currentStep === 'error' && (
              <div className="flex flex-col items-center justify-center space-y-6">
                <div className="text-center">
                  <AlertCircle className="w-16 h-16 text-red-500 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    处理失败
                  </h3>
                  <p className="text-sm text-gray-600 mb-4">
                    {state.error || '未知错误'}
                  </p>
                </div>

                <Button
                  onClick={() => setState(prev => ({ ...prev, currentStep: 'upload' }))}
                  variant="outline"
                >
                  重新开始
                </Button>
              </div>
            )}
          </div>

          {/* 底部操作按钮 */}
          <div className="flex justify-end gap-3 pt-4 border-t">
            <Button
              variant="outline"
              onClick={handleClose}
              disabled={state.isPolling}
            >
              {state.currentStep === 'completed' ? '关闭' : '取消'}
            </Button>
            
            {state.currentStep === 'config' && (
              <Button
                onClick={handleStartGeneration}
                className="bg-purple-500 hover:bg-purple-600"
                disabled={!state.selectedFile || !state.fileValidation.isValid}
              >
                开始生成
              </Button>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}

export default SubtitleGeneratorModal
