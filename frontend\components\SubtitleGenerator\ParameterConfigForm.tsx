/**
 * 参数配置表单组件
 * 用于配置字幕生成的各种参数
 */

"use client"

import React from 'react'
import { Info, Clock, Settings } from 'lucide-react'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import { Input } from '@/components/ui/input'
import { Slider } from '@/components/ui/slider'

import {
  SUPPORTED_LANGUAGES,
  TIMESTAMP_GRANULARITY_OPTIONS,
  DEFAULT_ADDITIONAL_FORMATS
} from './types'
import type {
  SubtitleConfig,
  AdditionalFormatsConfig
} from './types'

interface ParameterConfigFormProps {
  config: SubtitleConfig
  onChange: (config: Partial<SubtitleConfig>) => void
  disabled?: boolean
}

const ParameterConfigForm: React.FC<ParameterConfigFormProps> = ({
  config,
  onChange,
  disabled = false
}) => {
  // 解析 additional_formats 配置
  const parseAdditionalFormats = (jsonString: string): AdditionalFormatsConfig => {
    try {
      const parsed = JSON.parse(jsonString)
      return Array.isArray(parsed) && parsed.length > 0 ? parsed[0] : DEFAULT_ADDITIONAL_FORMATS
    } catch {
      return DEFAULT_ADDITIONAL_FORMATS
    }
  }

  // 当前的 additional_formats 配置
  const additionalFormats = parseAdditionalFormats(config.additional_formats)

  // 更新 additional_formats 配置
  const updateAdditionalFormats = (updates: Partial<AdditionalFormatsConfig>) => {
    const newConfig = { ...additionalFormats, ...updates }
    const jsonString = JSON.stringify([newConfig])
    onChange({ additional_formats: jsonString })
  }
  return (
    <TooltipProvider>
      <div className="space-y-6">
        {/* 基础设置 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="w-5 h-5" />
              基础设置
            </CardTitle>
            <CardDescription>
              配置字幕生成的基本参数
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* 语言选择 */}
            <div className="space-y-2">
              <Label htmlFor="language" className="flex items-center gap-2">
                语言
                <Tooltip>
                  <TooltipTrigger>
                    <Info className="w-4 h-4 text-gray-400" />
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>选择音频内容的主要语言，有助于提高识别准确性</p>
                  </TooltipContent>
                </Tooltip>
              </Label>
              <Select
                value={config.language_code}
                onValueChange={(value) => onChange({ language_code: value })}
                disabled={disabled}
              >
                <SelectTrigger>
                  <SelectValue placeholder="选择语言" />
                </SelectTrigger>
                <SelectContent>
                  {SUPPORTED_LANGUAGES.map((lang) => (
                    <SelectItem key={lang.code} value={lang.code}>
                      <div className="flex items-center gap-2">
                        <span>{lang.flag}</span>
                        <span>{lang.name}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* 时间戳粒度 */}
            <div className="space-y-2">
              <Label htmlFor="granularity" className="flex items-center gap-2">
                时间戳粒度
                <Tooltip>
                  <TooltipTrigger>
                    <Info className="w-4 h-4 text-gray-400" />
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>词级别：为每个词生成时间戳，更精确但文件更大</p>
                    <p>句子级别：为每个句子生成时间戳，文件更小</p>
                  </TooltipContent>
                </Tooltip>
              </Label>
              <Select
                value={config.timestamps_granularity}
                onValueChange={(value: 'word' | 'sentence') => onChange({ timestamps_granularity: value })}
                disabled={disabled}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {TIMESTAMP_GRANULARITY_OPTIONS.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      <div className="space-y-1">
                        <div className="font-medium">{option.label}</div>
                        <div className="text-sm text-gray-500">{option.description}</div>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* 音频事件标记 */}
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label htmlFor="audio-events" className="flex items-center gap-2">
                  音频事件标记
                  <Tooltip>
                    <TooltipTrigger>
                      <Info className="w-4 h-4 text-gray-400" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>标记音频中的非语音事件，如掌声、笑声、音乐等</p>
                    </TooltipContent>
                  </Tooltip>
                </Label>
                <p className="text-sm text-gray-500 whitespace-normal">
                  在字幕中标记掌声、笑声等音频事件
                </p>
              </div>
              <Switch
                id="audio-events"
                checked={config.tag_audio_events}
                onCheckedChange={(checked) => onChange({ tag_audio_events: checked })}
                disabled={disabled}
              />
            </div>
          </CardContent>
        </Card>

        {/* 自定义参数配置 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="w-5 h-5" />
              自定义参数
            </CardTitle>
            <CardDescription>
              配置字幕文件的详细生成选项
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* 每行最大字符数 */}
            <div className="space-y-3">
              <Label className="flex items-center gap-2">
                每行最大字符数
                <Tooltip>
                  <TooltipTrigger>
                    <Info className="w-4 h-4 text-gray-400" />
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>控制字幕每行显示的最大字符数量</p>
                    <p>范围: 1-200，推荐: 30-80</p>
                  </TooltipContent>
                </Tooltip>
              </Label>
              <div className="space-y-2">
                <Slider
                  value={[additionalFormats.max_characters_per_line]}
                  onValueChange={(value) => updateAdditionalFormats({ max_characters_per_line: value[0] })}
                  min={1}
                  max={200}
                  step={1}
                  disabled={disabled}
                  className="w-full"
                />
                <div className="flex justify-between text-sm text-gray-500">
                  <span>1</span>
                  <span className="font-medium">{additionalFormats.max_characters_per_line} 字符</span>
                  <span>200</span>
                </div>
              </div>
            </div>

            {/* 说话人标记 */}
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label className="flex items-center gap-2">
                  包含说话人标记
                  <Tooltip>
                    <TooltipTrigger>
                      <Info className="w-4 h-4 text-gray-400" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>在字幕文件中显示说话人标记</p>
                      <p>例如: [speaker_0] 你好</p>
                    </TooltipContent>
                  </Tooltip>
                </Label>
                <p className="text-sm text-gray-500">
                  在字幕中显示说话人标识
                </p>
              </div>
              <Switch
                checked={additionalFormats.include_speakers}
                onCheckedChange={(checked) => updateAdditionalFormats({ include_speakers: checked })}
                disabled={disabled}
              />
            </div>

            {/* 时间戳显示 */}
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label className="flex items-center gap-2">
                  包含时间戳
                  <Tooltip>
                    <TooltipTrigger>
                      <Info className="w-4 h-4 text-gray-400" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>在SRT文件中包含时间戳信息</p>
                      <p>通常建议保持启用</p>
                    </TooltipContent>
                  </Tooltip>
                </Label>
                <p className="text-sm text-gray-500">
                  SRT文件中的时间戳信息
                </p>
              </div>
              <Switch
                checked={additionalFormats.include_timestamps}
                onCheckedChange={(checked) => updateAdditionalFormats({ include_timestamps: checked })}
                disabled={disabled}
              />
            </div>

            {/* 静音分段阈值 */}
            <div className="space-y-3">
              <Label className="flex items-center gap-2">
                静音分段阈值 (秒)
                <Tooltip>
                  <TooltipTrigger>
                    <Info className="w-4 h-4 text-gray-400" />
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>当静音时长超过此阈值时进行分段</p>
                    <p>范围: 0.1-10.0秒</p>
                  </TooltipContent>
                </Tooltip>
              </Label>
              <div className="space-y-2">
                <Slider
                  value={[additionalFormats.segment_on_silence_longer_than_s]}
                  onValueChange={(value) => updateAdditionalFormats({ segment_on_silence_longer_than_s: value[0] })}
                  min={0.1}
                  max={10.0}
                  step={0.1}
                  disabled={disabled}
                  className="w-full"
                />
                <div className="flex justify-between text-sm text-gray-500">
                  <span>0.1s</span>
                  <span className="font-medium">{additionalFormats.segment_on_silence_longer_than_s.toFixed(1)}s</span>
                  <span>10.0s</span>
                </div>
              </div>
            </div>

            {/* 最大段落时长 */}
            <div className="space-y-3">
              <Label className="flex items-center gap-2">
                最大段落时长 (秒)
                <Tooltip>
                  <TooltipTrigger>
                    <Info className="w-4 h-4 text-gray-400" />
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>单个字幕段落的最大时长</p>
                    <p>范围: 1.0-30.0秒</p>
                  </TooltipContent>
                </Tooltip>
              </Label>
              <div className="space-y-2">
                <Slider
                  value={[additionalFormats.max_segment_duration_s]}
                  onValueChange={(value) => updateAdditionalFormats({ max_segment_duration_s: value[0] })}
                  min={1.0}
                  max={30.0}
                  step={0.5}
                  disabled={disabled}
                  className="w-full"
                />
                <div className="flex justify-between text-sm text-gray-500">
                  <span>1.0s</span>
                  <span className="font-medium">{additionalFormats.max_segment_duration_s.toFixed(1)}s</span>
                  <span>30.0s</span>
                </div>
              </div>
            </div>

            {/* 最大段落字符数 */}
            <div className="space-y-3">
              <Label className="flex items-center gap-2">
                最大段落字符数
                <Tooltip>
                  <TooltipTrigger>
                    <Info className="w-4 h-4 text-gray-400" />
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>单个字幕段落的最大字符数</p>
                    <p>范围: 10-500字符</p>
                  </TooltipContent>
                </Tooltip>
              </Label>
              <div className="space-y-2">
                <Slider
                  value={[additionalFormats.max_segment_chars]}
                  onValueChange={(value) => updateAdditionalFormats({ max_segment_chars: value[0] })}
                  min={10}
                  max={500}
                  step={5}
                  disabled={disabled}
                  className="w-full"
                />
                <div className="flex justify-between text-sm text-gray-500">
                  <span>10</span>
                  <span className="font-medium">{additionalFormats.max_segment_chars} 字符</span>
                  <span>500</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </TooltipProvider>
  )
}

export default ParameterConfigForm
